console.log('🔍 Import debug başlıyor...');

try {
  console.log('1. Business agent import ediliyor...');
  const { businessSummarizerAgent } = await import('./src/mastra/agents/business-summarizer.js');
  console.log('✅ Business agent başarıyla import edildi:', businessSummarizerAgent.name);
  
  console.log('2. Mastra import ediliyor...');
  const { mastra } = await import('./src/mastra/index.js');
  console.log('✅ Mastra başarıyla import edildi');
  
  console.log('3. Mastra agents kontrol ediliyor...');
  console.log('Mastra agents:', mastra.agents?.length || 0);
  
  if (mastra.agents && mastra.agents.length > 0) {
    mastra.agents.forEach((agent, index) => {
      console.log(`   Agent ${index + 1}: ${agent.name}`);
    });
  }
  
  console.log('4. getAgent test ediliyor...');
  const agent = mastra.getAgent('business-summarizer');
  console.log('✅ Agent bulundu:', agent ? agent.name : 'null');
  
} catch (error) {
  console.error('❌ Import hatası:', error.message);
  console.error('Stack:', error.stack);
}
