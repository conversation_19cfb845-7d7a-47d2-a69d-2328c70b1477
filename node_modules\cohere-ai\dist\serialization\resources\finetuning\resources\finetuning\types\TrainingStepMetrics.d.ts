/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../../../../../index";
import * as Cohere from "../../../../../../api/index";
import * as core from "../../../../../../core";
export declare const TrainingStepMetrics: core.serialization.ObjectSchema<serializers.finetuning.TrainingStepMetrics.Raw, Cohere.finetuning.TrainingStepMetrics>;
export declare namespace TrainingStepMetrics {
    interface Raw {
        created_at?: string | null;
        step_number?: number | null;
        metrics?: Record<string, number> | null;
    }
}
