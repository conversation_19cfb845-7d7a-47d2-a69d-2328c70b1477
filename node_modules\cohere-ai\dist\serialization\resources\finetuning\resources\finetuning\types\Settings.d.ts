/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../../../../../index";
import * as Cohere from "../../../../../../api/index";
import * as core from "../../../../../../core";
import { BaseModel } from "./BaseModel";
import { Hyperparameters } from "./Hyperparameters";
import { WandbConfig } from "./WandbConfig";
export declare const Settings: core.serialization.ObjectSchema<serializers.finetuning.Settings.Raw, Cohere.finetuning.Settings>;
export declare namespace Settings {
    interface Raw {
        base_model: BaseModel.Raw;
        dataset_id: string;
        hyperparameters?: Hyperparameters.Raw | null;
        multi_label?: boolean | null;
        wandb?: WandbConfig.Raw | null;
    }
}
