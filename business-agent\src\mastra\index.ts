
import { <PERSON><PERSON> } from '@mastra/core';
// import { MCPClient } from '@mastra/mcp';
import { businessSummarizerAgent } from './agents/business-summarizer.js';

console.log('🔍 Mastra index.ts yükleniyor...');
console.log('🔍 Agent import edildi:', businessSummarizerAgent?.name);

// MCP Client konfigürasyonu - şimdilik devre dışı
// const mcpClient = new MCPClient({
//   servers: {
//     'bsns-mcp': {
//       url: new URL('https://server.smithery.ai/@zeliha1/bsns-mcp/mcp?api_key=6134e80e-8e3e-4eb3-8482-ed8542124c32'),
//       requestInit: {
//         headers: {
//           'Content-Type': 'application/json'
//         }
//       }
//     }
//   }
// });

console.log('🔍 Mastra oluşturuluyor...');
export const mastra = new Mastra({
  agents: [businessSummarizerAgent]
  // tools: {
  //   mcpClient
  // }
});

console.log('✅ <PERSON>stra olu<PERSON>turuldu, agent say<PERSON>s<PERSON>:', mastra.agents?.length || 0);
if (mastra.agents && mastra.agents.length > 0) {
  mastra.agents.forEach((agent, index) => {
    console.log(`   Agent ${index + 1}: ${agent.name}`);
  });
}
