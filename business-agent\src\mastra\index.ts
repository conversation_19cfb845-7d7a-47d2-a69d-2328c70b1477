
import { <PERSON><PERSON> } from '@mastra/core';
// import { MCPClient } from '@mastra/mcp';
import { businessSummarizerAgent } from './agents/business-summarizer.js';

// MCP Client konfigürasyonu - şimdilik devre dışı
// const mcpClient = new MCPClient({
//   servers: {
//     'bsns-mcp': {
//       url: new URL('https://server.smithery.ai/@zeliha1/bsns-mcp/mcp?api_key=6134e80e-8e3e-4eb3-8482-ed8542124c32'),
//       requestInit: {
//         headers: {
//           'Content-Type': 'application/json'
//         }
//       }
//     }
//   }
// });

export const mastra = new Mastra({
  agents: [businessSummarizerAgent]
  // tools: {
  //   mcpClient
  // }
});
