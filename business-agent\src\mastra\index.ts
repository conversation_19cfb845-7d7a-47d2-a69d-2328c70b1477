
import { <PERSON><PERSON> } from '@mastra/core';
import { MCPClient } from '@mastra/mcp';
import { businessSummarizerAgent } from './agents/business-summarizer.js';

// MCP Client konfigürasyonu - Smithery URL'si ile
const mcpClient = new MCPClient({
  name: 'bsns-mcp-client',
  serverConfigs: [{
    name: 'bsns-mcp',
    transport: {
      type: 'http',
      url: 'https://server.smithery.ai/@zeliha1/bsns-mcp/mcp?api_key=6134e80e-8e3e-4eb3-8482-ed8542124c32'
    }
  }]
});

export const mastra = new Mastra({
  agents: [businessSummarizerAgent],
  tools: {
    mcpClient
  }
});
