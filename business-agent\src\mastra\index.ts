
import { <PERSON><PERSON> } from '@mastra/core';
import { MCPClient } from '@mastra/mcp';
import { businessSummarizerAgent } from './agents/business-summarizer.js';

// MCP Client konfigürasyonu - Smithery üzerinden bsns-mcp'ye bağ<PERSON>ıyor
const mcpClient = new MCPClient({
  name: 'bsns-mcp-client',
  serverName: 'bsns-mcp',
  transport: {
    type: 'http',
    url: 'https://server.smithery.ai/@zeliha1/bsns-mcp/mcp',
    headers: {
      'Authorization': 'Bearer 6134e80e-8e3e-4eb3-8482-ed8542124c32'
    }
  }
});

export const mastra = new Mastra({
  agents: [businessSummarizerAgent],
  tools: {
    mcpClient
  }
});
