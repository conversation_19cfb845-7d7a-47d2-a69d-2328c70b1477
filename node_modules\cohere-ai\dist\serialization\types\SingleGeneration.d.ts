/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as Cohere from "../../api/index";
import * as core from "../../core";
import { SingleGenerationTokenLikelihoodsItem } from "./SingleGenerationTokenLikelihoodsItem";
export declare const SingleGeneration: core.serialization.ObjectSchema<serializers.SingleGeneration.Raw, Cohere.SingleGeneration>;
export declare namespace SingleGeneration {
    interface Raw {
        id: string;
        text: string;
        index?: number | null;
        likelihood?: number | null;
        token_likelihoods?: SingleGenerationTokenLikelihoodsItem.Raw[] | null;
    }
}
