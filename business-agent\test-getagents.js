import { Agent } from '@mastra/core';
import { <PERSON><PERSON> } from '@mastra/core';
import { openai } from '@ai-sdk/openai';

console.log('🔍 getAgents test başlıyor...');

// Agent oluştur
const agent = new Agent({
  name: 'test-agent',
  instructions: 'Test agent',
  model: openai('gpt-4o-mini')
});

console.log('✅ Agent oluşturuldu:', agent.name);

// Mastra oluştur
const mastra = new Mastra({ agents: [agent] });
console.log('✅ Mastra oluşturuldu');

// getAgents metodunu dene
try {
  const agents = mastra.getAgents();
  console.log('📊 getAgents() sonucu:', agents);
  console.log('📊 Agent sayısı:', agents?.length || 0);
  
  if (agents && agents.length > 0) {
    console.log('📝 İlk agent:', agents[0].name);
  }
} catch (error) {
  console.error('❌ getAgents hatası:', error.message);
}

// getAgent metodunu dene
try {
  const foundAgent = mastra.getAgent('test-agent');
  console.log('✅ getAgent başarılı:', foundAgent.name);
} catch (error) {
  console.error('❌ getAgent hatası:', error.message);
}
