import { mastra } from './src/mastra/index.js';

async function testBusinessSummarizer() {
  console.log('🚀 Business Summarizer Agent Test Başlıyor...\n');

  try {
    // Agent'ı al
    const agent = mastra.getAgent('business-summarizer');
    
    if (!agent) {
      throw new Error('Business summarizer agent bulunamadı!');
    }

    console.log('✅ Agent başarıyla yüklendi');
    console.log('📝 Agent adı:', agent.name);
    console.log('🔧 Mevcut araçlar:', Object.keys(agent.tools || {}));
    console.log('\n');

    // Test URL'si - örnek bir iş haberi
    const testUrl = 'https://www.hurriyet.com.tr/ekonomi/';
    
    console.log(`📰 Test URL'si: ${testUrl}`);
    console.log('⏳ Özet oluşturuluyor...\n');

    // Agent'ı çalıştır
    const result = await agent.generate([
      {
        role: 'user',
        content: `Bu URL'deki iş haberini özetle: ${testUrl}`
      }
    ]);

    console.log('✅ Özet başarıyla oluşturuldu!');
    console.log('📄 Sonuç:');
    console.log('─'.repeat(50));
    console.log(result.text);
    console.log('─'.repeat(50));

  } catch (error) {
    console.error('❌ Test sırasında hata oluştu:', error);
    
    if (error instanceof Error) {
      console.error('Hata detayı:', error.message);
      console.error('Stack trace:', error.stack);
    }
  }
}

// Test'i çalıştır
testBusinessSummarizer();
