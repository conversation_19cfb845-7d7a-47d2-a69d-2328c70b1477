/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as Cohere from "../../api/index";
import * as core from "../../core";
import { ToolSource } from "./ToolSource";
import { DocumentSource } from "./DocumentSource";
export declare const Source: core.serialization.Schema<serializers.Source.Raw, Cohere.Source>;
export declare namespace Source {
    type Raw = Source.Tool | Source.Document;
    interface Tool extends ToolSource.Raw {
        type: "tool";
    }
    interface Document extends DocumentSource.Raw {
        type: "document";
    }
}
