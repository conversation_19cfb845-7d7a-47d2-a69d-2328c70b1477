import { <PERSON>stra } from '@mastra/core';

console.log('🔍 Mastra class kontrol ediliyor...');
console.log('📝 Mastra:', typeof Mastra);
console.log('📝 Mastra prototype:', Object.getOwnPropertyNames(Mastra.prototype));

const mastra = new Mastra({ agents: [] });
console.log('📝 Mastra instance:', Object.getOwnPropertyNames(mastra));
console.log('📝 Mastra agents property:', mastra.hasOwnProperty('agents'));

// Mastra'nın constructor'ını kontrol et
console.log('📝 Mastra constructor length:', Mastra.length);

// Manuel olarak agent ekle
try {
  console.log('\n🔍 Manuel agent ekleme test...');
  if (mastra.agents) {
    console.log('✅ agents property var');
  } else {
    console.log('❌ agents property yok, oluşturuluyor...');
    mastra.agents = [];
  }
} catch (error) {
  console.error('❌ Manuel ekleme hatası:', error.message);
}
