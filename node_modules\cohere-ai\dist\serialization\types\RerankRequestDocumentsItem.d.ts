/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as Cohere from "../../api/index";
import * as core from "../../core";
import { RerankDocument } from "./RerankDocument";
export declare const RerankRequestDocumentsItem: core.serialization.Schema<serializers.RerankRequestDocumentsItem.Raw, Cohere.RerankRequestDocumentsItem>;
export declare namespace RerankRequestDocumentsItem {
    type Raw = string | RerankDocument.Raw;
}
