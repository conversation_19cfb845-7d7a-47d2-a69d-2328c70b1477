/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as Cohere from "../../api/index";
import * as core from "../../core";
import { ChatMessageStartEvent } from "./ChatMessageStartEvent";
import { ChatContentStartEvent } from "./ChatContentStartEvent";
import { ChatContentDeltaEvent } from "./ChatContentDeltaEvent";
import { ChatContentEndEvent } from "./ChatContentEndEvent";
import { ChatToolPlanDeltaEvent } from "./ChatToolPlanDeltaEvent";
import { ChatToolCallStartEvent } from "./ChatToolCallStartEvent";
import { ChatToolCallDeltaEvent } from "./ChatToolCallDeltaEvent";
import { ChatToolCallEndEvent } from "./ChatToolCallEndEvent";
import { CitationStartEvent } from "./CitationStartEvent";
import { CitationEndEvent } from "./CitationEndEvent";
import { ChatMessageEndEvent } from "./ChatMessageEndEvent";
import { ChatDebugEvent } from "./ChatDebugEvent";
export declare const StreamedChatResponseV2: core.serialization.Schema<serializers.StreamedChatResponseV2.Raw, Cohere.StreamedChatResponseV2>;
export declare namespace StreamedChatResponseV2 {
    type Raw = StreamedChatResponseV2.MessageStart | StreamedChatResponseV2.ContentStart | StreamedChatResponseV2.ContentDelta | StreamedChatResponseV2.ContentEnd | StreamedChatResponseV2.ToolPlanDelta | StreamedChatResponseV2.ToolCallStart | StreamedChatResponseV2.ToolCallDelta | StreamedChatResponseV2.ToolCallEnd | StreamedChatResponseV2.CitationStart | StreamedChatResponseV2.CitationEnd | StreamedChatResponseV2.MessageEnd | StreamedChatResponseV2.Debug;
    interface MessageStart extends ChatMessageStartEvent.Raw {
        type: "message-start";
    }
    interface ContentStart extends ChatContentStartEvent.Raw {
        type: "content-start";
    }
    interface ContentDelta extends ChatContentDeltaEvent.Raw {
        type: "content-delta";
    }
    interface ContentEnd extends ChatContentEndEvent.Raw {
        type: "content-end";
    }
    interface ToolPlanDelta extends ChatToolPlanDeltaEvent.Raw {
        type: "tool-plan-delta";
    }
    interface ToolCallStart extends ChatToolCallStartEvent.Raw {
        type: "tool-call-start";
    }
    interface ToolCallDelta extends ChatToolCallDeltaEvent.Raw {
        type: "tool-call-delta";
    }
    interface ToolCallEnd extends ChatToolCallEndEvent.Raw {
        type: "tool-call-end";
    }
    interface CitationStart extends CitationStartEvent.Raw {
        type: "citation-start";
    }
    interface CitationEnd extends CitationEndEvent.Raw {
        type: "citation-end";
    }
    interface MessageEnd extends ChatMessageEndEvent.Raw {
        type: "message-end";
    }
    interface Debug extends ChatDebugEvent.Raw {
        type: "debug";
    }
}
