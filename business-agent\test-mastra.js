import { mastra } from './src/mastra/index.js';

console.log('🚀 Mastra Business Agent Test Başlıyor...\n');

async function testAgent() {
  try {
    console.log('📋 Mastra konfigürasyonu kontrol ediliyor...');

    // Agent'ları listele
    const agents = mastra.agents || [];
    console.log(`✅ ${agents.length} agent bulundu`);

    if (agents.length > 0) {
      agents.forEach((agent, index) => {
        console.log(`   ${index + 1}. ${agent.name}`);
      });
    }

    // Tools'ları listele
    const tools = mastra.tools || {};
    console.log(`🔧 ${Object.keys(tools).length} tool bulundu`);
    Object.keys(tools).forEach((toolName, index) => {
      console.log(`   ${index + 1}. ${toolName}`);
    });

    console.log('\n📝 Business summarizer agentı test ediyoruz...');

    // Agent'ı al
    const agent = mastra.getAgent('business-summarizer');

    if (!agent) {
      throw new Error('Business summarizer agent bulunamadı!');
    }

    console.log('✅ Agent başarıyla bulundu');
    console.log('🔧 Agent tools:', Object.keys(agent.tools || {}));

    // Basit bir test mesajı gönder
    console.log('\n⏳ Test mesajı gönderiliyor...');

    const testUrl = 'https://www.hurriyet.com.tr/ekonomi/';
    const result = await agent.generate([
      {
        role: 'user',
        content: `Bu URL'deki iş haberini özetle: ${testUrl}`
      }
    ]);

    console.log('\n✅ Agent yanıtı alındı!');
    console.log('📄 Sonuç:');
    console.log('─'.repeat(50));
    console.log(result.text);
    console.log('─'.repeat(50));

  } catch (error) {
    console.error('\n❌ Test sırasında hata oluştu:', error.message);

    if (error.stack) {
      console.error('Stack trace:', error.stack);
    }
  }
}

testAgent();
