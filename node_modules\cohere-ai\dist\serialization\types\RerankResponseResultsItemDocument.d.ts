/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as Cohere from "../../api/index";
import * as core from "../../core";
export declare const RerankResponseResultsItemDocument: core.serialization.ObjectSchema<serializers.RerankResponseResultsItemDocument.Raw, Cohere.RerankResponseResultsItemDocument>;
export declare namespace RerankResponseResultsItemDocument {
    interface Raw {
        text: string;
    }
}
