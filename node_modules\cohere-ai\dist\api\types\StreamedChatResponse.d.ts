/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
import * as Cohere from "../index";
/**
 * StreamedChatResponse is returned in streaming mode (specified with `stream=True` in the request).
 */
export declare type StreamedChatResponse = Cohere.StreamedChatResponse.StreamStart | Cohere.StreamedChatResponse.SearchQueriesGeneration | Cohere.StreamedChatResponse.SearchResults | Cohere.StreamedChatResponse.TextGeneration | Cohere.StreamedChatResponse.CitationGeneration | Cohere.StreamedChatResponse.ToolCallsGeneration | Cohere.StreamedChatResponse.StreamEnd | Cohere.StreamedChatResponse.ToolCallsChunk | Cohere.StreamedChatResponse.Debug;
export declare namespace StreamedChatResponse {
    interface StreamStart extends Cohere.ChatStreamStartEvent {
        eventType: "stream-start";
    }
    interface SearchQueriesGeneration extends Cohere.ChatSearchQueriesGenerationEvent {
        eventType: "search-queries-generation";
    }
    interface SearchResults extends Cohere.ChatSearchResultsEvent {
        eventType: "search-results";
    }
    interface TextGeneration extends Cohere.ChatTextGenerationEvent {
        eventType: "text-generation";
    }
    interface CitationGeneration extends Cohere.ChatCitationGenerationEvent {
        eventType: "citation-generation";
    }
    interface ToolCallsGeneration extends Cohere.ChatToolCallsGenerationEvent {
        eventType: "tool-calls-generation";
    }
    interface StreamEnd extends Cohere.ChatStreamEndEvent {
        eventType: "stream-end";
    }
    interface ToolCallsChunk extends Cohere.ChatToolCallsChunkEvent {
        eventType: "tool-calls-chunk";
    }
    interface Debug extends Cohere.ChatDebugEvent {
        eventType: "debug";
    }
}
