(()=>{var A;function U(B){return U=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(C){return typeof C}:function(C){return C&&typeof Symbol=="function"&&C.constructor===Symbol&&C!==Symbol.prototype?"symbol":typeof C},U(B)}function x(B,C){var G=Object.keys(B);if(Object.getOwnPropertySymbols){var H=Object.getOwnPropertySymbols(B);C&&(H=H.filter(function(J){return Object.getOwnPropertyDescriptor(B,J).enumerable})),G.push.apply(G,H)}return G}function Q(B){for(var C=1;C<arguments.length;C++){var G=arguments[C]!=null?arguments[C]:{};C%2?x(Object(G),!0).forEach(function(H){N(B,H,G[H])}):Object.getOwnPropertyDescriptors?Object.defineProperties(B,Object.getOwnPropertyDescriptors(G)):x(Object(G)).forEach(function(H){Object.defineProperty(B,H,Object.getOwnPropertyDescriptor(G,H))})}return B}function N(B,C,G){if(C=z(C),C in B)Object.defineProperty(B,C,{value:G,enumerable:!0,configurable:!0,writable:!0});else B[C]=G;return B}function z(B){var C=E(B,"string");return U(C)=="symbol"?C:String(C)}function E(B,C){if(U(B)!="object"||!B)return B;var G=B[Symbol.toPrimitive];if(G!==void 0){var H=G.call(B,C||"default");if(U(H)!="object")return H;throw new TypeError("@@toPrimitive must return a primitive value.")}return(C==="string"?String:Number)(B)}var W=Object.defineProperty,GB=function B(C,G){for(var H in G)W(C,H,{get:G[H],enumerable:!0,configurable:!0,set:function J(X){return G[H]=function(){return X}}})},D={lessThanXSeconds:{one:"mens d\u2019una segonda",other:"mens de {{count}} segondas"},xSeconds:{one:"1 segonda",other:"{{count}} segondas"},halfAMinute:"30 segondas",lessThanXMinutes:{one:"mens d\u2019una minuta",other:"mens de {{count}} minutas"},xMinutes:{one:"1 minuta",other:"{{count}} minutas"},aboutXHours:{one:"environ 1 ora",other:"environ {{count}} oras"},xHours:{one:"1 ora",other:"{{count}} oras"},xDays:{one:"1 jorn",other:"{{count}} jorns"},aboutXWeeks:{one:"environ 1 setmana",other:"environ {{count}} setmanas"},xWeeks:{one:"1 setmana",other:"{{count}} setmanas"},aboutXMonths:{one:"environ 1 mes",other:"environ {{count}} meses"},xMonths:{one:"1 mes",other:"{{count}} meses"},aboutXYears:{one:"environ 1 an",other:"environ {{count}} ans"},xYears:{one:"1 an",other:"{{count}} ans"},overXYears:{one:"mai d\u2019un an",other:"mai de {{count}} ans"},almostXYears:{one:"gaireben un an",other:"gaireben {{count}} ans"}},S=function B(C,G,H){var J,X=D[C];if(typeof X==="string")J=X;else if(G===1)J=X.one;else J=X.other.replace("{{count}}",String(G));if(H!==null&&H!==void 0&&H.addSuffix)if(H.comparison&&H.comparison>0)return"d\u2019aqu\xED "+J;else return"fa "+J;return J};function $(B){return function(){var C=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},G=C.width?String(C.width):B.defaultWidth,H=B.formats[G]||B.formats[B.defaultWidth];return H}}var M={full:"EEEE d 'de' MMMM y",long:"d 'de' MMMM y",medium:"d MMM y",short:"dd/MM/y"},R={full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},L={full:"{{date}} 'a' {{time}}",long:"{{date}} 'a' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},V={date:$({formats:M,defaultWidth:"full"}),time:$({formats:R,defaultWidth:"full"}),dateTime:$({formats:L,defaultWidth:"full"})},j={lastWeek:"eeee 'passat a' p",yesterday:"'i\xE8r a' p",today:"'u\xE8i a' p",tomorrow:"'deman a' p",nextWeek:"eeee 'a' p",other:"P"},w=function B(C,G,H,J){return j[C]};function I(B){return function(C,G){var H=G!==null&&G!==void 0&&G.context?String(G.context):"standalone",J;if(H==="formatting"&&B.formattingValues){var X=B.defaultFormattingWidth||B.defaultWidth,Y=G!==null&&G!==void 0&&G.width?String(G.width):X;J=B.formattingValues[Y]||B.formattingValues[X]}else{var Z=B.defaultWidth,q=G!==null&&G!==void 0&&G.width?String(G.width):B.defaultWidth;J=B.values[q]||B.values[Z]}var T=B.argumentCallback?B.argumentCallback(C):C;return J[T]}}var _={narrow:["ab. J.C.","apr. J.C."],abbreviated:["ab. J.C.","apr. J.C."],wide:["abans J\xE8sus-Crist","apr\xE8s J\xE8sus-Crist"]},f={narrow:["T1","T2","T3","T4"],abbreviated:["1\xE8r trim.","2nd trim.","3en trim.","4en trim."],wide:["1\xE8r trim\xE8stre","2nd trim\xE8stre","3en trim\xE8stre","4en trim\xE8stre"]},v={narrow:["GN","FB","M\xC7","AB","MA","JN","JL","AG","ST","OC","NV","DC"],abbreviated:["gen.","febr.","mar\xE7","abr.","mai","junh","jul.","ag.","set.","oct.","nov.","dec."],wide:["geni\xE8r","febri\xE8r","mar\xE7","abril","mai","junh","julhet","agost","setembre","oct\xF2bre","novembre","decembre"]},P={narrow:["dg.","dl.","dm.","dc.","dj.","dv.","ds."],short:["dg.","dl.","dm.","dc.","dj.","dv.","ds."],abbreviated:["dg.","dl.","dm.","dc.","dj.","dv.","ds."],wide:["dimenge","diluns","dimars","dim\xE8cres","dij\xF2us","divendres","dissabte"]},F={narrow:{am:"am",pm:"pm",midnight:"mi\xE8janu\xE8ch",noon:"mi\xE8gjorn",morning:"matin",afternoon:"apr\xE8p-mi\xE8gjorn",evening:"v\xE8spre",night:"nu\xE8ch"},abbreviated:{am:"a.m.",pm:"p.m.",midnight:"mi\xE8janu\xE8ch",noon:"mi\xE8gjorn",morning:"matin",afternoon:"apr\xE8p-mi\xE8gjorn",evening:"v\xE8spre",night:"nu\xE8ch"},wide:{am:"a.m.",pm:"p.m.",midnight:"mi\xE8janu\xE8ch",noon:"mi\xE8gjorn",morning:"matin",afternoon:"apr\xE8p-mi\xE8gjorn",evening:"v\xE8spre",night:"nu\xE8ch"}},k={narrow:{am:"am",pm:"pm",midnight:"mi\xE8janu\xE8ch",noon:"mi\xE8gjorn",morning:"del matin",afternoon:"de l\u2019apr\xE8p-mi\xE8gjorn",evening:"del ser",night:"de la nu\xE8ch"},abbreviated:{am:"AM",pm:"PM",midnight:"mi\xE8janu\xE8ch",noon:"mi\xE8gjorn",morning:"del matin",afternoon:"de l\u2019apr\xE8p-mi\xE8gjorn",evening:"del ser",night:"de la nu\xE8ch"},wide:{am:"ante meridiem",pm:"post meridiem",midnight:"mi\xE8janu\xE8ch",noon:"mi\xE8gjorn",morning:"del matin",afternoon:"de l\u2019apr\xE8p-mi\xE8gjorn",evening:"del ser",night:"de la nu\xE8ch"}},b=function B(C,G){var H=Number(C),J=G===null||G===void 0?void 0:G.unit,X;switch(H){case 1:X="\xE8r";break;case 2:X="nd";break;default:X="en"}if(J==="year"||J==="week"||J==="hour"||J==="minute"||J==="second")X+="a";return H+X},h={ordinalNumber:b,era:I({values:_,defaultWidth:"wide"}),quarter:I({values:f,defaultWidth:"wide",argumentCallback:function B(C){return C-1}}),month:I({values:v,defaultWidth:"wide"}),day:I({values:P,defaultWidth:"wide"}),dayPeriod:I({values:F,defaultWidth:"wide",formattingValues:k,defaultFormattingWidth:"wide"})};function O(B){return function(C){var G=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},H=G.width,J=H&&B.matchPatterns[H]||B.matchPatterns[B.defaultMatchWidth],X=C.match(J);if(!X)return null;var Y=X[0],Z=H&&B.parsePatterns[H]||B.parsePatterns[B.defaultParseWidth],q=Array.isArray(Z)?y(Z,function(K){return K.test(Y)}):m(Z,function(K){return K.test(Y)}),T;T=B.valueCallback?B.valueCallback(q):q,T=G.valueCallback?G.valueCallback(T):T;var CB=C.slice(Y.length);return{value:T,rest:CB}}}function m(B,C){for(var G in B)if(Object.prototype.hasOwnProperty.call(B,G)&&C(B[G]))return G;return}function y(B,C){for(var G=0;G<B.length;G++)if(C(B[G]))return G;return}function c(B){return function(C){var G=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},H=C.match(B.matchPattern);if(!H)return null;var J=H[0],X=C.match(B.parsePattern);if(!X)return null;var Y=B.valueCallback?B.valueCallback(X[0]):X[0];Y=G.valueCallback?G.valueCallback(Y):Y;var Z=C.slice(J.length);return{value:Y,rest:Z}}}var g=/^(\d+)(èr|nd|en)?[a]?/i,d=/\d+/i,p={narrow:/^(ab\.J\.C|apr\.J\.C|apr\.J\.-C)/i,abbreviated:/^(ab\.J\.-C|ab\.J-C|apr\.J\.-C|apr\.J-C|ap\.J-C)/i,wide:/^(abans Jèsus-Crist|après Jèsus-Crist)/i},u={any:[/^ab/i,/^ap/i]},l={narrow:/^T[1234]/i,abbreviated:/^[1234](èr|nd|en)? trim\.?/i,wide:/^[1234](èr|nd|en)? trimèstre/i},i={any:[/1/i,/2/i,/3/i,/4/i]},n={narrow:/^(GN|FB|MÇ|AB|MA|JN|JL|AG|ST|OC|NV|DC)/i,abbreviated:/^(gen|febr|març|abr|mai|junh|jul|ag|set|oct|nov|dec)\.?/i,wide:/^(genièr|febrièr|març|abril|mai|junh|julhet|agost|setembre|octòbre|novembre|decembre)/i},s={any:[/^g/i,/^f/i,/^ma[r?]|MÇ/i,/^ab/i,/^ma[i?]/i,/^ju[n?]|JN/i,/^ju[l?]|JL/i,/^ag/i,/^s/i,/^o/i,/^n/i,/^d/i]},o={narrow:/^d[glmcjvs]\.?/i,short:/^d[glmcjvs]\.?/i,abbreviated:/^d[glmcjvs]\.?/i,wide:/^(dimenge|diluns|dimars|dimècres|dijòus|divendres|dissabte)/i},r={narrow:[/^dg/i,/^dl/i,/^dm/i,/^dc/i,/^dj/i,/^dv/i,/^ds/i],short:[/^dg/i,/^dl/i,/^dm/i,/^dc/i,/^dj/i,/^dv/i,/^ds/i],abbreviated:[/^dg/i,/^dl/i,/^dm/i,/^dc/i,/^dj/i,/^dv/i,/^ds/i],any:[/^dg|dime/i,/^dl|dil/i,/^dm|dima/i,/^dc|dimè/i,/^dj|dij/i,/^dv|div/i,/^ds|dis/i]},e={any:/(^(a\.?m|p\.?m))|(ante meridiem|post meridiem)|((del |de la |de l’)(matin|aprèp-miègjorn|vèspre|ser|nuèch))/i},a={any:{am:/(^a)|ante meridiem/i,pm:/(^p)|post meridiem/i,midnight:/^mièj/i,noon:/^mièg/i,morning:/matin/i,afternoon:/aprèp-miègjorn/i,evening:/vèspre|ser/i,night:/nuèch/i}},t={ordinalNumber:c({matchPattern:g,parsePattern:d,valueCallback:function B(C){return parseInt(C,10)}}),era:O({matchPatterns:p,defaultMatchWidth:"wide",parsePatterns:u,defaultParseWidth:"any"}),quarter:O({matchPatterns:l,defaultMatchWidth:"wide",parsePatterns:i,defaultParseWidth:"any",valueCallback:function B(C){return C+1}}),month:O({matchPatterns:n,defaultMatchWidth:"wide",parsePatterns:s,defaultParseWidth:"any"}),day:O({matchPatterns:o,defaultMatchWidth:"wide",parsePatterns:r,defaultParseWidth:"any"}),dayPeriod:O({matchPatterns:e,defaultMatchWidth:"any",parsePatterns:a,defaultParseWidth:"any"})},BB={code:"oc",formatDistance:S,formatLong:V,formatRelative:w,localize:h,match:t,options:{weekStartsOn:1,firstWeekContainsDate:4}};window.dateFns=Q(Q({},window.dateFns),{},{locale:Q(Q({},(A=window.dateFns)===null||A===void 0?void 0:A.locale),{},{oc:BB})})})();

//# debugId=7450E5521DE5134D64756E2164756E21
