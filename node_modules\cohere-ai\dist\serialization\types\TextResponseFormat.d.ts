/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as Cohere from "../../api/index";
import * as core from "../../core";
export declare const TextResponseFormat: core.serialization.ObjectSchema<serializers.TextResponseFormat.Raw, Cohere.TextResponseFormat>;
export declare namespace TextResponseFormat {
    interface Raw {
    }
}
