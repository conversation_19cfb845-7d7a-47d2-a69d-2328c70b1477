/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
import * as Cohere from "../index";
/**
 * StreamedChatResponse is returned in streaming mode (specified with `stream=True` in the request).
 */
export declare type StreamedChatResponseV2 = Cohere.StreamedChatResponseV2.MessageStart | Cohere.StreamedChatResponseV2.ContentStart | Cohere.StreamedChatResponseV2.ContentDelta | Cohere.StreamedChatResponseV2.ContentEnd | Cohere.StreamedChatResponseV2.ToolPlanDelta | Cohere.StreamedChatResponseV2.ToolCallStart | Cohere.StreamedChatResponseV2.ToolCallDelta | Cohere.StreamedChatResponseV2.ToolCallEnd | Cohere.StreamedChatResponseV2.CitationStart | Cohere.StreamedChatResponseV2.CitationEnd | Cohere.StreamedChatResponseV2.MessageEnd | Cohere.StreamedChatResponseV2.Debug;
export declare namespace StreamedChatResponseV2 {
    interface MessageStart extends Cohere.ChatMessageStartEvent {
        type: "message-start";
    }
    interface ContentStart extends Cohere.ChatContentStartEvent {
        type: "content-start";
    }
    interface ContentDelta extends Cohere.ChatContentDeltaEvent {
        type: "content-delta";
    }
    interface ContentEnd extends Cohere.ChatContentEndEvent {
        type: "content-end";
    }
    interface ToolPlanDelta extends Cohere.ChatToolPlanDeltaEvent {
        type: "tool-plan-delta";
    }
    interface ToolCallStart extends Cohere.ChatToolCallStartEvent {
        type: "tool-call-start";
    }
    interface ToolCallDelta extends Cohere.ChatToolCallDeltaEvent {
        type: "tool-call-delta";
    }
    interface ToolCallEnd extends Cohere.ChatToolCallEndEvent {
        type: "tool-call-end";
    }
    interface CitationStart extends Cohere.CitationStartEvent {
        type: "citation-start";
    }
    interface CitationEnd extends Cohere.CitationEndEvent {
        type: "citation-end";
    }
    interface MessageEnd extends Cohere.ChatMessageEndEvent {
        type: "message-end";
    }
    interface Debug extends Cohere.ChatDebugEvent {
        type: "debug";
    }
}
