Metadata-Version: 2.4
Name: feedfinder2
Version: 0.0.4
Summary: Find the feed URLs for a website.
Home-page: https://github.com/dfm/feedfinder2
Author: <PERSON>-<PERSON>
Author-email: <EMAIL>
License: MIT
Classifier: Programming Language :: Python
Classifier: Development Status :: 4 - Beta
Classifier: Natural Language :: English
Classifier: Environment :: Web Environment
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: OS Independent
Requires-Dist: six
Requires-Dist: requests
Requires-Dist: beautifulsoup4
Dynamic: author
Dynamic: author-email
Dynamic: classifier
Dynamic: description
Dynamic: home-page
Dynamic: license
Dynamic: requires-dist
Dynamic: summary

Feedfinder2
===========

This is a Python library for finding links feeds on a website. It is based on
`feedfinder <http://www.aaronsw.com/2002/feedfinder/>`_ - originally
written by `<PERSON> <http://en.wikipedia.org/wiki/<PERSON>_<PERSON>_(software_developer)>`_ and
subsequently maintained by `<PERSON> <http://en.wikipedia.org/wiki/<PERSON>_<PERSON>>`_ until his untimely death.

Usage
-----

Feedfinder2 offers a single public function: ``find_feeds``. You would use it
as follows:

::

    from feedfinder2 import find_feeds
    feeds = find_feeds("xkcd.com")

Now, ``feeds`` is the list: ``['http://xkcd.com/atom.xml',
'http://xkcd.com/rss.xml']``. There is some attempt made to rank feeds from
best candidate to worst but... well... you never know.

License
-------

Feedfinder2 is licensed under the MIT license (see LICENSE).
