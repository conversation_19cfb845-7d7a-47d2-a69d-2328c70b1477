/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
import * as Cohere from "../index";
/**
 * Configuration for forcing the model output to adhere to the specified format. Supported on [Command R 03-2024](https://docs.cohere.com/docs/command-r), [Command R+ 04-2024](https://docs.cohere.com/docs/command-r-plus) and newer models.
 *
 * The model can be forced into outputting JSON objects (with up to 5 levels of nesting) by setting `{ "type": "json_object" }`.
 *
 * A [JSON Schema](https://json-schema.org/) can optionally be provided, to ensure a specific structure.
 *
 * **Note**: When using  `{ "type": "json_object" }` your `message` should always explicitly instruct the model to generate a JSON (eg: _"Generate a JSON ..."_) . Otherwise the model may end up getting stuck generating an infinite stream of characters and eventually run out of context length.
 * **Limitation**: The parameter is not supported in RAG mode (when any of `connectors`, `documents`, `tools`, `tool_results` are provided).
 */
export declare type ResponseFormat = Cohere.ResponseFormat.Text | Cohere.ResponseFormat.JsonObject;
export declare namespace ResponseFormat {
    interface Text extends Cohere.TextResponseFormat {
        type: "text";
    }
    interface JsonObject extends Cohere.JsonResponseFormat {
        type: "json_object";
    }
}
