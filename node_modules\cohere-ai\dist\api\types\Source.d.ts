/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as Cohere from "../index";
/**
 * A source object containing information about the source of the data cited.
 */
export declare type Source = Cohere.Source.Tool | Cohere.Source.Document;
export declare namespace Source {
    interface Tool extends Cohere.ToolSource {
        type: "tool";
    }
    interface Document extends Cohere.DocumentSource {
        type: "document";
    }
}
