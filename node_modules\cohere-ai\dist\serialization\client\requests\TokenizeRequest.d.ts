/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../../index";
import * as Cohere from "../../../api/index";
import * as core from "../../../core";
export declare const TokenizeRequest: core.serialization.Schema<serializers.TokenizeRequest.Raw, Cohere.TokenizeRequest>;
export declare namespace TokenizeRequest {
    interface Raw {
        text: string;
        model: string;
    }
}
