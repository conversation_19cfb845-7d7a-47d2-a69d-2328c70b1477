# Business Agent - <PERSON>ş Haberleri Özetleme Aracı

Bu proje, Mastra framework'ü kullanarak oluşturulmuş bir iş haberleri özetleme agent'ıdır. Agent, MCP (Model Context Protocol) üzerinden bsns-mcp servisini kullanarak web sitelerindeki iş haberlerini özetler.

## 🚀 Özellikler

- **İş Haberleri Özetleme**: Verilen URL'lerden iş haberlerini otomatik olarak özetler
- **Türkçe Destek**: Özetleri Türkçe olarak sunar
- **MCP Entegrasyonu**: Smithery üzerinden bsns-mcp servisini kullanır
- **AI Destekli**: OpenAI GPT-4o-mini modeli ile güçlendirilmiş
- **Mastra Framework**: Modern agent geliştirme framework'ü

## 📋 Gereksinimler

- Node.js >= 20.9.0
- npm veya yarn
- OpenAI API Key
- MCP Server erişimi (Smithery)

## 🛠️ Kurulum

1. **Projeyi klonlayın:**
```bash
git clone <repository-url>
cd business-agent
```

2. **Bağımlılıkları yükleyin:**
```bash
npm install
```

3. **Environment değişkenlerini ayarlayın:**
`.env` dosyasını düzenleyin:
```env
OPENAI_API_KEY=your_openai_api_key_here
MCP_SERVER_URL=https://server.smithery.ai/@zeliha1/bsns-mcp/mcp?api_key=your_api_key
MCP_API_KEY=your_mcp_api_key
```

## 🎯 Kullanım

### Development Modunda Çalıştırma
```bash
npm run dev
```

### Test Çalıştırma
```bash
npx tsx test-agent.ts
```

### Build
```bash
npm run build
```

## 🤖 Agent Kullanımı

Agent'ı programatik olarak kullanmak için:

```typescript
import { mastra } from './src/mastra/index.js';

async function summarizeArticle(url: string) {
  const agent = mastra.getAgent('business-summarizer');
  
  const result = await agent.generate([
    {
      role: 'user',
      content: `Bu URL'deki iş haberini özetle: ${url}`
    }
  ]);
  
  console.log(result.text);
}
```

## 📁 Proje Yapısı

```
business-agent/
├── src/
│   └── mastra/
│       ├── agents/
│       │   └── business-summarizer.ts    # Ana agent dosyası
│       ├── tools/                        # Özel araçlar (boş)
│       └── index.ts                      # Mastra konfigürasyonu
├── test-agent.ts                         # Test dosyası
├── .env                                  # Environment değişkenleri
├── package.json
└── README.md
```

## 🔧 Konfigürasyon

### Agent Ayarları
- **Model**: OpenAI GPT-4o-mini
- **Dil**: Türkçe
- **Özet Uzunluğu**: 3-5 cümle
- **Özellikler**: Finansal veriler, şirket adları, sektör bilgileri

### MCP Entegrasyonu
Agent, Smithery platformu üzerinden bsns-mcp servisini kullanır:
- **Servis**: `summarize_business_article`
- **Protokol**: HTTP REST API
- **Format**: JSON

## 🧪 Test Etme

Test dosyası (`test-agent.ts`) agent'ın çalışıp çalışmadığını kontrol eder:

```bash
npx tsx test-agent.ts
```

Test çıktısı:
- Agent yükleme durumu
- Mevcut araçlar listesi
- Örnek URL ile özet oluşturma
- Hata durumları

## 🔍 Sorun Giderme

### Yaygın Hatalar

1. **Agent bulunamadı**: 
   - `src/mastra/index.ts` dosyasında agent'ın doğru import edildiğinden emin olun

2. **MCP bağlantı hatası**:
   - `.env` dosyasındaki MCP URL ve API key'i kontrol edin
   - Smithery servisinin çalışır durumda olduğunu doğrulayın

3. **OpenAI API hatası**:
   - API key'in doğru ve geçerli olduğunu kontrol edin
   - Rate limit'e takılmadığınızdan emin olun

### Debug Modu

Detaylı log'lar için:
```bash
DEBUG=mastra:* npm run dev
```

## 📚 Daha Fazla Bilgi

- [Mastra Documentation](https://mastra.ai/docs)
- [MCP Protocol](https://modelcontextprotocol.io/)
- [OpenAI API](https://platform.openai.com/docs)

## 🤝 Katkıda Bulunma

1. Fork yapın
2. Feature branch oluşturun (`git checkout -b feature/amazing-feature`)
3. Commit yapın (`git commit -m 'Add amazing feature'`)
4. Push yapın (`git push origin feature/amazing-feature`)
5. Pull Request açın

## 📄 Lisans

Bu proje MIT lisansı altında lisanslanmıştır.
