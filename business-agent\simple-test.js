console.log('🚀 Business Agent Test Başlıyor...');

// Basit MCP test
async function testMCP() {
  try {
    console.log('📡 MCP servisini test ediyoruz...');

    // Önce tools listesini alalım
    const toolsResponse = await fetch('https://server.smithery.ai/@zeliha1/bsns-mcp/mcp?api_key=6134e80e-8e3e-4eb3-8482-ed8542124c32', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        jsonrpc: '2.0',
        id: 1,
        method: 'tools/list'
      })
    });

    console.log('🔧 Tools list response status:', toolsResponse.status);

    if (toolsResponse.ok) {
      const toolsResult = await toolsResponse.json();
      console.log('🔧 Available tools:', JSON.stringify(toolsResult, null, 2));
    }

    // Şimdi tool'u çağıralım
    const response = await fetch('https://server.smithery.ai/@zeliha1/bsns-mcp/mcp?api_key=6134e80e-8e3e-4eb3-8482-ed8542124c32', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        jsonrpc: '2.0',
        id: 2,
        method: 'tools/call',
        params: {
          name: 'summarize_business_article',
          arguments: {
            url: 'https://www.hurriyet.com.tr/ekonomi/'
          }
        }
      })
    });

    console.log('📊 Response status:', response.status);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    console.log('✅ MCP Response:', result);

  } catch (error) {
    console.error('❌ MCP Test hatası:', error.message);
  }
}

testMCP();
