import 'dotenv/config';
import { mastra } from './src/mastra/index.js';

console.log('🚀 Business Agent Test Başlıyor...\n');

async function test() {
  try {
    console.log('📋 Agent kontrol ediliyor...');

    // getAgents() kullan <PERSON> getAgent() çalışmıyor
    const agents = mastra.getAgents();
    console.log('📊 Mevcut agents:', Object.keys(agents));

    // İlk agent'ı al (business-summarizer olmalı)
    const agent = Object.values(agents)[0];
    if (!agent) {
      throw new Error('Hiç agent bulunamadı');
    }

    console.log('✅ Agent bulundu:', agent.name);

    console.log('\n📰 Test URL ile özet oluşturuluyor...');
    const testUrl = 'https://www.hurriyet.com.tr/ekonomi/merkez-bankasi-faiz-karari-********';

    const result = await agent.generate([
      {
        role: 'user',
        content: `Bu URL'deki iş haberini özetle: ${testUrl}`
      }
    ]);

    console.log('\n✅ Sonuç:');
    console.log('─'.repeat(50));
    console.log(result.text);
    console.log('─'.repeat(50));

  } catch (error) {
    console.error('❌ Hata:', error.message);
    if (error.stack) {
      console.error('Stack:', error.stack);
    }
  }
}

test();
