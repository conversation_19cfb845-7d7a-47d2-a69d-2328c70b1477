/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as Cohere from "../../api/index";
import * as core from "../../core";
import { SystemMessageContentItem } from "./SystemMessageContentItem";
export declare const SystemMessageContent: core.serialization.Schema<serializers.SystemMessageContent.Raw, Cohere.SystemMessageContent>;
export declare namespace SystemMessageContent {
    type Raw = string | SystemMessageContentItem.Raw[];
}
