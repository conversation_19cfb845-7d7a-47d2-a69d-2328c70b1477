/**
 * This file was auto-generated by Fern from our API Definition.
 */
/**
 * One of `low`, `medium`, `high`, or `auto`, defaults to `auto`. Controls how close to the original text the summary is. `high` extractiveness summaries will lean towards reusing sentences verbatim, while `low` extractiveness summaries will tend to paraphrase more. If `auto` is selected, the best option will be picked based on the input text.
 */
export declare type SummarizeRequestExtractiveness = "low" | "medium" | "high";
export declare const SummarizeRequestExtractiveness: {
    readonly Low: "low";
    readonly Medium: "medium";
    readonly High: "high";
};
