/**
 * This file was auto-generated by Fern from our API Definition.
 */
/**
 * The possible stages of a fine-tuned model life-cycle.
 *
 *  - STATUS_UNSPECIFIED: Unspecified status.
 *  - STATUS_FINETUNING: The fine-tuned model is being fine-tuned.
 *  - STATUS_DEPLOYING_API: Deprecated: The fine-tuned model is being deployed.
 *  - STATUS_READY: The fine-tuned model is ready to receive requests.
 *  - STATUS_FAILED: The fine-tuned model failed.
 *  - STATUS_DELETED: The fine-tuned model was deleted.
 *  - STATUS_TEMPORARILY_OFFLINE: Deprecated: The fine-tuned model is temporarily unavailable.
 *  - STATUS_PAUSED: Deprecated: The fine-tuned model is paused (Vanilla only).
 *  - STATUS_QUEUED: The fine-tuned model is queued for training.
 */
export declare type Status = "STATUS_UNSPECIFIED" | "STATUS_FINETUNING" | "STATUS_DEPLOYING_API" | "STATUS_READY" | "STATUS_FAILED" | "STATUS_DELETED" | "STATUS_TEMPORARILY_OFFLINE" | "STATUS_PAUSED" | "STATUS_QUEUED";
export declare const Status: {
    readonly StatusUnspecified: "STATUS_UNSPECIFIED";
    readonly StatusFinetuning: "STATUS_FINETUNING";
    readonly StatusDeployingApi: "STATUS_DEPLOYING_API";
    readonly StatusReady: "STATUS_READY";
    readonly StatusFailed: "STATUS_FAILED";
    readonly StatusDeleted: "STATUS_DELETED";
    readonly StatusTemporarilyOffline: "STATUS_TEMPORARILY_OFFLINE";
    readonly StatusPaused: "STATUS_PAUSED";
    readonly StatusQueued: "STATUS_QUEUED";
};
