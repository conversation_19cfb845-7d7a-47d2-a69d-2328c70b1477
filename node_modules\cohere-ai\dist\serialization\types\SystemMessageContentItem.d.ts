/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as Cohere from "../../api/index";
import * as core from "../../core";
import { TextContent } from "./TextContent";
export declare const SystemMessageContentItem: core.serialization.Schema<serializers.SystemMessageContentItem.Raw, Cohere.SystemMessageContentItem>;
export declare namespace SystemMessageContentItem {
    type Raw = SystemMessageContentItem.Text;
    interface Text extends TextContent.Raw {
        type: "text";
    }
}
