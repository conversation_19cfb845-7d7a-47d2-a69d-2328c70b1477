import { Agent } from '@mastra/core';
import { openai } from '@ai-sdk/openai';
import { z } from 'zod';

// Basit Business Article Summarizer Agent
export const businessSummarizerAgent = new Agent({
  name: 'business-summarizer',
  instructions: 'Sen bir iş haberleri uzmanısın. Verilen URL\'lerden iş haberlerini Türkçe olarak özetlersin.',
  model: openai('gpt-4o-mini'),
  tools: {
    summarize_business_article: {
      description: 'İş haberlerini özetler',
      parameters: z.object({
        url: z.string().url().describe('Özetlenecek haberin URL\'si')
      }),
      execute: async ({ url }) => {
        try {
          const response = await fetch('https://server.smithery.ai/@zeliha1/bsns-mcp/mcp?api_key=6134e80e-8e3e-4eb3-8482-ed8542124c32', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              jsonrpc: '2.0',
              id: 1,
              method: 'tools/call',
              params: {
                name: 'summarize_business_article',
                arguments: { url }
              }
            })
          });

          const result = await response.json();
          return result.result || 'Özet oluşturulamadı';
        } catch (error) {
          return `Hata: ${error.message}`;
        }
      }
    }
  }
});
