import { Agent } from '@mastra/core';
import { openai } from '@ai-sdk/openai';
import { z } from 'zod';

// Business Article Summarizer Agent
export const businessSummarizerAgent = new Agent({
  name: 'business-summarizer',
  instructions: `
    Sen bir iş haberleri uzmanısın. Görevin verilen URL'lerden iş haberlerini özetlemek.

    Özellikler:
    - İş haberlerini Türkçe olarak özetleyeceksin
    - Önemli noktaları vurgulayacaksın
    - Finansal veriler varsa bunları belirteceksin
    - Şirket adları ve sektör bilgilerini dahil edeceksin
    - Özet 3-5 cümle arasında olacak

    Kullanabileceğin araçlar:
    - summarize_business_article: Verilen URL'den iş haberini özetler
  `,
  model: openai('gpt-4o-mini'),
  tools: {
    summarize_business_article: {
      description: 'İş haberlerini özetlemek için kullanılan araç',
      parameters: z.object({
        url: z.string().url().describe('Özetlenecek iş haberinin URL\'si')
      }),
      execute: async ({ url }, { mastra }) => {
        try {
          // Mastra'nın MCP client'ını kullan
          const mcpClient = mastra.tools.mcpClient;

          if (!mcpClient) {
            throw new Error('MCP client bulunamadı');
          }

          // MCP client üzerinden tool'u çağır
          const result = await mcpClient.callTool('summarize_business_article', { url });

          return result || 'Özet oluşturulamadı';
        } catch (error) {
          console.error('MCP çağrısında hata:', error);

          // Fallback: Direct HTTP call
          try {
            const mcpUrl = process.env.MCP_SERVER_URL || 'https://server.smithery.ai/@zeliha1/bsns-mcp/mcp';
            const apiKey = process.env.MCP_API_KEY || '6134e80e-8e3e-4eb3-8482-ed8542124c32';

            const response = await fetch(`${mcpUrl}?api_key=${apiKey}`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                jsonrpc: '2.0',
                id: Date.now(),
                method: 'tools/call',
                params: {
                  name: 'summarize_business_article',
                  arguments: { url }
                }
              })
            });

            if (!response.ok) {
              throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();
            return result.result?.[0]?.text || result.result || 'Özet oluşturulamadı';
          } catch (fallbackError) {
            console.error('Fallback çağrısında da hata:', fallbackError);
            return `Hata: ${error instanceof Error ? error.message : 'Bilinmeyen hata'}`;
          }
        }
      }
    }
  }
});
