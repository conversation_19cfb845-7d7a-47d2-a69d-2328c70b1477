/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as Cohere from "../../api/index";
import * as core from "../../core";
export declare const SummarizeRequestLength: core.serialization.Schema<serializers.SummarizeRequestLength.Raw, Cohere.SummarizeRequestLength>;
export declare namespace SummarizeRequestLength {
    type Raw = "short" | "medium" | "long";
}
