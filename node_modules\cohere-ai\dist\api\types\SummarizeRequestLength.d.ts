/**
 * This file was auto-generated by Fern from our API Definition.
 */
/**
 * One of `short`, `medium`, `long`, or `auto` defaults to `auto`. Indicates the approximate length of the summary. If `auto` is selected, the best option will be picked based on the input text.
 */
export declare type SummarizeRequestLength = "short" | "medium" | "long";
export declare const SummarizeRequestLength: {
    readonly Short: "short";
    readonly Medium: "medium";
    readonly Long: "long";
};
