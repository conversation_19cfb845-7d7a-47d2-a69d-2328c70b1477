/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
import * as Cohere from "../index";
/**
 * Configuration for forcing the model output to adhere to the specified format. Supported on [Command R](https://docs.cohere.com/v2/docs/command-r), [Command R+](https://docs.cohere.com/v2/docs/command-r-plus) and newer models.
 *
 * The model can be forced into outputting JSON objects by setting `{ "type": "json_object" }`.
 *
 * A [JSON Schema](https://json-schema.org/) can optionally be provided, to ensure a specific structure.
 *
 * **Note**: When using  `{ "type": "json_object" }` your `message` should always explicitly instruct the model to generate a JSON (eg: _"Generate a JSON ..."_) . Otherwise the model may end up getting stuck generating an infinite stream of characters and eventually run out of context length.
 *
 * **Note**: When `json_schema` is not specified, the generated object can have up to 5 layers of nesting.
 *
 * **Limitation**: The parameter is not supported when used in combinations with the `documents` or `tools` parameters.
 */
export declare type ResponseFormatV2 = Cohere.ResponseFormatV2.Text | Cohere.ResponseFormatV2.JsonObject;
export declare namespace ResponseFormatV2 {
    interface Text extends Cohere.TextResponseFormatV2 {
        type: "text";
    }
    interface JsonObject extends Cohere.JsonResponseFormatV2 {
        type: "json_object";
    }
}
