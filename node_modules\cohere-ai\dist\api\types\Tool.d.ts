/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as Cohere from "../index";
export interface Tool {
    /** The name of the tool to be called. Valid names contain only the characters `a-z`, `A-Z`, `0-9`, `_` and must not begin with a digit. */
    name: string;
    /** The description of what the tool does, the model uses the description to choose when and how to call the function. */
    description: string;
    /**
     * The input parameters of the tool. Accepts a dictionary where the key is the name of the parameter and the value is the parameter spec. Valid parameter names contain only the characters `a-z`, `A-Z`, `0-9`, `_` and must not begin with a digit.
     * ```
     * {
     *   "my_param": {
     *     "description": <string>,
     *     "type": <string>, // any python data type, such as 'str', 'bool'
     *     "required": <boolean>
     *   }
     * }
     * ```
     */
    parameterDefinitions?: Record<string, Cohere.ToolParameterDefinitionsValue>;
}
