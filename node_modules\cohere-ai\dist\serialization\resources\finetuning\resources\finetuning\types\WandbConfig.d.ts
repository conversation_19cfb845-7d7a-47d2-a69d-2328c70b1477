/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../../../../../index";
import * as Cohere from "../../../../../../api/index";
import * as core from "../../../../../../core";
export declare const WandbConfig: core.serialization.ObjectSchema<serializers.finetuning.WandbConfig.Raw, Cohere.finetuning.WandbConfig>;
export declare namespace WandbConfig {
    interface Raw {
        project: string;
        api_key: string;
        entity?: string | null;
    }
}
