import { Agent } from '@mastra/core';
import { <PERSON><PERSON> } from '@mastra/core';
import { openai } from '@ai-sdk/openai';

console.log('🔍 Minimal test başlıyor...');

// Direkt agent oluştur
const testAgent = new Agent({
  name: 'test-agent',
  instructions: '<PERSON> bir test agentısın.',
  model: openai('gpt-4o-mini')
});

console.log('✅ Agent oluşturuldu:', testAgent.name);

// Mastra oluştur
const testMastra = new Mastra({
  agents: [testAgent]
});

console.log('✅ Mastra oluşturuldu');
console.log('📊 Agent sayısı:', testMastra.agents?.length || 0);

// Agent'ı bul
try {
  const foundAgent = testMastra.getAgent('test-agent');
  console.log('✅ Agent bulundu:', foundAgent.name);
} catch (error) {
  console.error('❌ Agent bulunamadı:', error.message);
}
