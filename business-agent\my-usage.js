import 'dotenv/config';
import { mastra } from './src/mastra/index.js';

async function summarizeNews(url) {
  try {
    // Agent'ı al
    const agents = mastra.getAgents();
    const agent = Object.values(agents)[0]; // business-summarizer
    
    if (!agent) {
      throw new Error('Agent bulunamadı');
    }
    
    console.log(`📰 Haber özetleniyor: ${url}`);
    
    // Agent'ı çalıştır
    const result = await agent.generate([
      {
        role: 'user',
        content: `Bu URL'deki iş haberini özetle: ${url}`
      }
    ]);
    
    return result.text;
  } catch (error) {
    console.error('❌ Hata:', error.message);
    return null;
  }
}

// Kullanım örnekleri
async function main() {
  console.log('🚀 Business Agent Kullanım Örnekleri\n');
  
  // Örnek 1: Tek haber özetleme
  const url1 = 'https://www.hurriyet.com.tr/ekonomi/merkez-bankasi-faiz-karari-********';
  const summary1 = await summarizeNews(url1);
  if (summary1) {
    console.log('📄 Özet 1:');
    console.log(summary1);
    console.log('\n' + '─'.repeat(50) + '\n');
  }
  
  // Örnek 2: Farklı bir haber
  const url2 = 'https://www.aa.com.tr/tr/ekonomi/';
  const summary2 = await summarizeNews(url2);
  if (summary2) {
    console.log('📄 Özet 2:');
    console.log(summary2);
    console.log('\n' + '─'.repeat(50) + '\n');
  }
  
  // Örnek 3: Birden fazla haber özetleme
  const urls = [
    'https://www.hurriyet.com.tr/ekonomi/',
    'https://www.milliyet.com.tr/ekonomi/',
    'https://www.sabah.com.tr/ekonomi/'
  ];
  
  console.log('📰 Toplu haber özetleme:');
  for (const url of urls) {
    const summary = await summarizeNews(url);
    if (summary) {
      console.log(`\n🔗 ${url}`);
      console.log(`📄 ${summary}`);
    }
  }
}

// Eğer bu dosya direkt çalıştırılıyorsa main fonksiyonunu çalıştır
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

// Export et ki başka dosyalardan kullanılabilsin
export { summarizeNews };
