/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as Cohere from "../../api/index";
import * as core from "../../core";
import { ApiMeta } from "./ApiMeta";
export declare const SummarizeResponse: core.serialization.ObjectSchema<serializers.SummarizeResponse.Raw, Cohere.SummarizeResponse>;
export declare namespace SummarizeResponse {
    interface Raw {
        id?: string | null;
        summary?: string | null;
        meta?: ApiMeta.Raw | null;
    }
}
