import { Agent } from '@mastra/core';
import { Mastra } from '@mastra/core';
import { openai } from '@ai-sdk/openai';

console.log('🔍 Mastra debug başlıyor...');

// Agent oluştur
const agent = new Agent({
  name: 'debug-agent',
  instructions: 'Test agent',
  model: openai('gpt-4o-mini')
});

console.log('✅ Agent oluşturuldu:', agent);
console.log('📝 Agent name:', agent.name);
console.log('📝 Agent type:', typeof agent);

// Mastra oluştur - farklı yöntemler dene
console.log('\n🔍 Mastra oluşturma test 1...');
try {
  const mastra1 = new Mastra();
  console.log('✅ Boş Mastra oluşturuldu');
  console.log('📊 Agents:', mastra1.agents?.length || 0);
} catch (error) {
  console.error('❌ Boş Mastra hatası:', error.message);
}

console.log('\n🔍 Mastra oluşturma test 2...');
try {
  const mastra2 = new Mastra({});
  console.log('✅ Boş config ile Mastra oluşturuldu');
  console.log('📊 Agents:', mastra2.agents?.length || 0);
} catch (error) {
  console.error('❌ Boş config Mastra hatası:', error.message);
}

console.log('\n🔍 Mastra oluşturma test 3...');
try {
  const mastra3 = new Mastra({ agents: [] });
  console.log('✅ Boş agents array ile Mastra oluşturuldu');
  console.log('📊 Agents:', mastra3.agents?.length || 0);
} catch (error) {
  console.error('❌ Boş agents array Mastra hatası:', error.message);
}

console.log('\n🔍 Mastra oluşturma test 4...');
try {
  const mastra4 = new Mastra({ agents: [agent] });
  console.log('✅ Agent ile Mastra oluşturuldu');
  console.log('📊 Agents:', mastra4.agents?.length || 0);
  console.log('📊 Agents array:', mastra4.agents);
  
  if (mastra4.agents && mastra4.agents.length > 0) {
    console.log('📝 İlk agent:', mastra4.agents[0].name);
  }
} catch (error) {
  console.error('❌ Agent ile Mastra hatası:', error.message);
  console.error('Stack:', error.stack);
}
