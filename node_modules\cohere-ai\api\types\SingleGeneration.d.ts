/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as Cohere from "../index";
export interface SingleGeneration {
    id: string;
    text: string;
    /** Refers to the nth generation. Only present when `num_generations` is greater than zero. */
    index?: number;
    likelihood?: number;
    /** Only returned if `return_likelihoods` is set to `GENERATION` or `ALL`. The likelihood refers to the average log-likelihood of the entire specified string, which is useful for [evaluating the performance of your model](likelihood-eval), especially if you've created a [custom model](https://docs.cohere.com/docs/training-custom-models). Individual token likelihoods provide the log-likelihood of each token. The first token will not have a likelihood. */
    tokenLikelihoods?: Cohere.SingleGenerationTokenLikelihoodsItem[];
}
