/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as Cohere from "../../api/index";
import * as core from "../../core";
import { RerankResponseResultsItem } from "./RerankResponseResultsItem";
import { ApiMeta } from "./ApiMeta";
export declare const RerankResponse: core.serialization.ObjectSchema<serializers.RerankResponse.Raw, Cohere.RerankResponse>;
export declare namespace RerankResponse {
    interface Raw {
        id?: string | null;
        results: RerankResponseResultsItem.Raw[];
        meta?: ApiMeta.Raw | null;
    }
}
